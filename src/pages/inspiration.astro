---
import Layout from "../components/layout/Layout.astro";
import InspirationHero from "../components/inspiration/InspirationHero.astro";
import SkiingTypesWithStays from "../components/inspiration/SkiingTypesWithStays.astro";
import CTASection from "../components/home/<USER>";

// Define page metadata
const title = "Inspiration - Perfect Piste - Luxury Ski Holidays";
const description =
  "Discover your perfect ski experience with our curated stays and expert recommendations for every type of skiing adventure, from family-friendly slopes to off-piste adventures.";
---

<Layout title={title} description={description}>
  <!-- Hero Section -->
  <InspirationHero
    title="Find Your Perfect Ski Experience"
    description="Discover curated stays and expert recommendations tailored to your skiing style"
    backgroundImage="https://images.unsplash.com/photo-1551524164-6cf2ac2d7d1c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
  />

  <!-- Skiing Types with Featured Stays Section -->
  <SkiingTypesWithStays />

  <!-- CTA Section -->
  <CTASection
    title="Ready to Plan Your Perfect Ski Holiday?"
    description="Let our experts help you find the ideal combination of accommodation and skiing experience tailored to your preferences."
    ctaText="START PLANNING"
    ctaLink="/ai-search"
    backgroundImage="https://images.unsplash.com/photo-1486870591958-9b9d0d1dda99?ixlib=rb-4.0.3&auto=format&fit=crop&w=2576&q=80"
  />
</Layout>
