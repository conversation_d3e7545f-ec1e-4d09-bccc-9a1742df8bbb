import { CopilotChat } from "@copilotkit/react-ui";
import { useCoAgent, useCopilotChat } from "@copilotkit/react-core";
import { CustomUserMessage } from "./CustomMessages";
import React, { useEffect, useState } from "react";
import { Role, TextMessage } from "@copilotkit/runtime-client-gql";
import AssistantMessageWithSuggestions from "./AssistantMessageWithSuggestions";
import type { Hotel } from "./hotel/types";
import type { SkiDestination } from "./destination/types";

interface PerfectPisteAIProps {
  initialUserMessage?: string;
}

// Interface for suggestion data from Assistant State
interface SuggestionData {
  intro_text?: string; // Optional introduction text
  suggestions: string[]; // Array of suggestion strings
}

export const PerfectPisteAI: React.FC<PerfectPisteAIProps> = ({
  initialUserMessage,
}) => {
  const { state, setState } = useCoAgent({
    name: "sample_agent",
    initialState: {
      suggestions: [],
      hotels: [],
      destinations: [], // Array of destinations to display
      destinationId: null as string | null, // ID of the selected destination for filtering hotels
      selectedHotelId: null as string | null, // Track which hotel the user has selected
      selectedDestinationId: null as string | null, // Track which destination the user has selected
    },
  });

  // This will validate that the CopilotKit context is properly provided
  const [mounted, setMounted] = useState(false);
  const [initialMessageProcessed, setInitialMessageProcessed] = useState(false);

  // Use useEffect to ensure the component is mounted before rendering
  useEffect(() => {
    setMounted(true);
  }, []);

  // Get the copilot chat API
  const { appendMessage, visibleMessages } = useCopilotChat();

  // Function to send a user message to the chat
  const sendUserMessage = (content: string) => {
    console.log("Adding user message:", content);

    // Create a new user message with a unique ID
    const userMessage = new TextMessage({
      content,
      role: Role.User,
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    });

    // Append the message with followUp: true to trigger the AI response
    appendMessage(userMessage, { followUp: true });
  };

  // Process the initial user message if provided
  useEffect(() => {
    if (mounted && initialUserMessage && !initialMessageProcessed) {
      // Add a small delay to ensure the chat is fully initialized
      const timer = setTimeout(() => {
        // Send the user message to the chat using the proper API
        sendUserMessage(initialUserMessage);
        setInitialMessageProcessed(true);
      }, 1500); // 1.5 second delay to ensure the chat is fully loaded

      return () => clearTimeout(timer);
    }
  }, [mounted, initialUserMessage, initialMessageProcessed, appendMessage]);

  // Extract suggestions from the Assistant State
  const suggestionData: SuggestionData = {
    intro_text:
      (state as any)?.intro_text || "✨ Here are a few ways I can help you:",
    suggestions: state?.suggestions || [],
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    // Send the suggestion as a user message
    sendUserMessage(suggestion);

    // Clear suggestions, hotels, and destinations from the state
    setState({
      ...state,
      suggestions: [],
      hotels: [], // Clear the hotels array when a suggestion is clicked
      destinations: [], // Clear the destinations array when a suggestion is clicked
      destinationId: null, // Reset destinationId as well
      selectedDestinationId: null, // Reset selectedDestinationId as well
    } as any);

    console.log("Cleared suggestions, hotels, and destinations after click");
  };

  if (!mounted) {
    return (
      <div className="h-full flex items-center justify-center">
        Loading chat interface...
      </div>
    );
  }

  // Create a wrapper component that passes the suggestions and hotels to AssistantMessageWithSuggestions
  const AssistantMessageWrapper = (props: any) => {
    // Get all assistant messages
    const assistantMessages = visibleMessages.filter(
      (m: any) => m.role === Role.Assistant
    );

    // Check if this is the last assistant message
    const isLastMessage =
      assistantMessages.length > 0 &&
      props.message?.id === assistantMessages[assistantMessages.length - 1]?.id;

    // Handle hotel selection
    const handleSelectHotel = (hotel: Hotel) => {
      setState({
        ...state,
        selectedHotelId: hotel.id,
        hotels: [], // Clear the hotels array to hide hotel listings
        suggestions: [], // Clear suggestions to hide suggestion chips
      });

      // Send an automatic message to the chat
      const message = `I'd like to book ${hotel.name} hotel`;
      sendUserMessage(message);
    };

    // Handle destination selection
    const handleSelectDestination = (destination: SkiDestination) => {
      console.log("Selected destination in chat:", destination);

      // Update the state with the selected destination ID and clear destinations/suggestions
      setState({
        ...state,
        selectedDestinationId: destination.id,
        destinationId: destination.id, // Set the destinationId for filtering hotels
        destinations: [], // Clear the destinations array to hide destination listings
        suggestions: [], // Clear suggestions to hide suggestion chips
      });

      // Send an automatic message to the chat
      const message = `I'm interested in skiing in ${destination.name}`;
      sendUserMessage(message);
    };

    return (
      <AssistantMessageWithSuggestions
        {...props}
        suggestions={suggestionData.suggestions}
        introText={suggestionData.intro_text}
        onSuggestionClick={handleSuggestionClick}
        isLastMessage={isLastMessage}
        hotels={state?.hotels || []}
        destinations={state?.destinations || []}
        destinationId={state?.destinationId || undefined}
        onSelectHotel={handleSelectHotel}
        onSelectDestination={handleSelectDestination}
      />
    );
  };

  return (
    <div
      className="ai-search-mobile-container container relative rounded-lg h-[85vh] sm:h-[90vh] lg:h-[92vh] mt-16 sm:mt-8 lg:mt-0 px-2 sm:px-4 lg:px-6"
      style={{ overflow: "visible" }}
    >
      <CopilotChat
        className="h-full w-full flex flex-col ai-search-chat-interface"
        labels={{
          title: "Luma - Your Luxury Ski Concierge",
        }}
        UserMessage={CustomUserMessage}
        AssistantMessage={AssistantMessageWrapper}
      />
    </div>
  );
};
