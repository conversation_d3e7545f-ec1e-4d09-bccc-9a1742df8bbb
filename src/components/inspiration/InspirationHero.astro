---
interface Props {
  title: string;
  description: string;
  backgroundImage: string;
}

const { title, description, backgroundImage } = Astro.props;
---

<section
  class="relative h-[80vh] min-h-[600px] flex items-center justify-center overflow-hidden"
>
  <!-- Background Image -->
  <div class="absolute inset-0">
    <img
      src={backgroundImage}
      alt="Inspiration Hero"
      class="w-full h-full object-cover scale-105 transition-transform duration-[10s] ease-out"
    />
    <div
      class="absolute inset-0 bg-gradient-to-b from-black/30 via-black/40 to-black/60"
    >
    </div>
  </div>

  <!-- Floating Elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      class="absolute top-20 left-10 w-2 h-2 bg-white/20 rounded-full animate-pulse"
    >
    </div>
    <div
      class="absolute top-40 right-20 w-1 h-1 bg-white/30 rounded-full animate-pulse"
      style="animation-delay: 1s;"
    >
    </div>
    <div
      class="absolute bottom-32 left-20 w-1.5 h-1.5 bg-white/25 rounded-full animate-pulse"
      style="animation-delay: 2s;"
    >
    </div>
  </div>

  <!-- Content -->
  <div class="relative z-10 text-center text-white px-4 max-w-5xl mx-auto">
    <div class="mb-8">
      <div
        class="inline-flex items-center justify-center w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full mb-8 border border-white/20"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
          <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
        </svg>
      </div>
    </div>

    <h1
      class="text-5xl md:text-6xl lg:text-7xl font-baskervville mb-8 leading-tight tracking-wide"
    >
      {title}
    </h1>
    <p
      class="text-xl md:text-2xl font-karla max-w-3xl mx-auto leading-relaxed text-white/90 mb-12"
    >
      {description}
    </p>

    <!-- CTA Buttons -->
    <div
      class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
    >
      <a
        href="#skiing-experiences"
        class="group inline-flex items-center bg-white text-gray-900 px-8 py-4 rounded-full font-karla font-semibold text-sm uppercase tracking-wider transition-all duration-300 hover:bg-gray-100 hover:shadow-xl hover:-translate-y-1"
      >
        Explore Experiences
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2 transition-transform duration-300 group-hover:translate-x-1"
        >
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </a>

      <a
        href="/ai-search"
        class="group inline-flex items-center border-2 border-white/30 text-white px-8 py-4 rounded-full font-karla font-semibold text-sm uppercase tracking-wider transition-all duration-300 hover:bg-white/10 hover:border-white/50 backdrop-blur-sm"
      >
        AI Search
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2 transition-transform duration-300 group-hover:scale-110"
        >
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
      </a>
    </div>

    <!-- Scroll indicator -->
    <div class="animate-bounce">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="text-white/60 mx-auto"
      >
        <path d="M7 13l3 3 7-7"></path>
        <path d="M12 17V7"></path>
      </svg>
    </div>
  </div>
</section>

<style>
  @keyframes bounce {
    0%,
    20%,
    53%,
    80%,
    100% {
      transform: translate3d(-50%, 0, 0);
    }
    40%,
    43% {
      transform: translate3d(-50%, -8px, 0);
    }
    70% {
      transform: translate3d(-50%, -4px, 0);
    }
    90% {
      transform: translate3d(-50%, -2px, 0);
    }
  }
</style>
